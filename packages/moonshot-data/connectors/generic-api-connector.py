

import json
import aiohttp
from moonshot.src.connectors.connector import Connector, perform_retry
from moonshot.src.connectors.connector_response \
    import ConnectorResponse
from moonshot.src.utils.log import configure_logger

# Create a logger for this module
logger = configure_logger(__name__)


class GenericAPIConnector(Connector):
    """
    Generic API Connector class for any text-to-text API.

    - API Input can be defined with:
       - The text input, e.g.: `{"input": "query.text"}` means given `This is the prompt`, the API will be called with `{"query": {"text": "This is the prompt"}}`
       - Any additional input, e.g.: `{"input": "query.text", "additional_input": {"key": "value"}}` means given `This is the prompt`, the API will be called with `{"query": {"text": "This is the prompt"}, "key": "value"}`
    - API Output can be defined with:
       - The text output, e.g.: `{"output": "response.text"}` means given `{"response": {"text": "This is the response"}}`, the API will return `This is the response`
    - Authentication can be configured with the `auth_config` parameter:
       - Header-based auth: `{"auth_config": {"type": "header", "value": {"Authorization": "Bearer {token}"}}}`
       - No auth: `{"auth_config": {"type": "none"}}`

    The `{token}` placeholder in auth_config values will be replaced with the token value from the endpoint configuration.
    If no auth_config is provided, the default behavior is to use `{"Authorization": token}` for backward compatibility.
    """  # noqa: E501

    def __init__(self, ep_arguments):
        # Initialize super class
        super().__init__(ep_arguments)

        if "input" not in self.params:
            raise ValueError(
                "Input must be defined in the connector parameters"
            )
        if "output" not in self.params:
            raise ValueError(
                "Output must be defined in the connector parameters"
            )

        self.additional_input = self.params.get("additional_input", {})
        if not isinstance(self.additional_input, dict):
            raise ValueError("Additional input must be a dictionary")

        # Extract auth_config from params if available
        self.auth_config = self.params.get("auth_config", None)
        # Validate auth_config if provided
        if self.auth_config is not None:
            if not isinstance(self.auth_config, dict):
                raise ValueError("auth_config must be a dictionary")
            if "type" not in self.auth_config:
                raise ValueError("auth_config must contain a 'type' field")
            if self.auth_config["type"] not in ["header", "none"]:
                raise ValueError(
                    "auth_config type must be one of: 'header', 'none'")
            if self.auth_config["type"] != "none" and "value" not in self.auth_config:
                raise ValueError(
                    "auth_config must contain a 'value' field when type is not 'none'")

    @classmethod
    def build_payload(cls, prompt: str, input_spec: dict) -> str | dict:
        """
        Given a prompt string and an input_spec dictionary of the form:
          {"input": "query.text"}
        this function returns a nested dictionary where the JSONPath "query.text"
        is interpreted as nested keys with the prompt as the final value.

        If "input" is not specified or is empty string, the function returns prompt as is.

        For example, if prompt is "This is the prompt", the function will return:
          {"query": {"text": "This is the prompt"}}
        """  # noqa: E501
        # Get the JSONPath string from the input specification.
        path = input_spec.get("input", "")
        if path == "":
            return prompt

        # Split the JSONPath on dots.
        keys = path.split(".")

        # Build the nested dictionary.
        result = {}
        current = result
        for key in keys[:-1]:
            current[key] = {}
            current = current[key]

        # Set the final key to the prompt value.
        current[keys[-1]] = prompt
        return result

    @classmethod
    def extract_output(cls, data: str | dict, output_spec: dict) -> str:
        """
        Given a data dictionary and an output specification of the form:
          {"output": "response.text"}
        this function traverses the data using the dot-separated path and
        returns the corresponding value.

        If "output" is not specified or is empty string, the function returns data as is.

        For example:
          data = {"response": {"text": "This is the response"}}
          output_spec = {"output": "response.text"}
        will return:
          "This is the response"
        """
        path = output_spec.get("output", "")
        if path == "":
            return data

        # Split the path by dot (.)
        keys = path.split(".")

        # Traverse the dictionary according to the keys
        value = data
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                raise ValueError(
                    f"Key {key} not found in {output_spec}. Value: {json.dumps(data)}"  # noqa: E501
                )
        return value

    def _build_headers(self):
        """
        Build request headers based on auth_config.

        Returns:
            dict: Headers to be used in the API request
        """
        # Default to empty headers
        headers = {}

        # If auth_config is not provided, use the legacy header format for backward compatibility
        if self.auth_config is None:
            logger.debug("Using legacy x-api-key authentication")
            headers = {"x-api-key": self.token}
            return headers

        # Process based on auth_config type
        auth_type = self.auth_config["type"]

        if auth_type == "none":
            # No authentication headers needed
            logger.debug("No authentication headers added")
            return headers

        if auth_type == "header":
            logger.debug(f"Using header-based authentication")
            # Add headers from auth_config
            value = self.auth_config["value"]
            if isinstance(value, dict):
                # Process each header, replacing {token} with the actual token
                for key, val in value.items():
                    logger.debug(f"Adding header: {key}")
                    if isinstance(val, str):
                        headers[key] = val.replace("{token}", self.token)
                    else:
                        headers[key] = val
            else:
                logger.warning(
                    "auth_config 'value' should be a dictionary for type 'header'")

        return headers

    @Connector.rate_limited
    @perform_retry
    async def get_response(self, prompt: str) -> ConnectorResponse:
        # Use aiohttp to send the prompt to the API
        # and return the response
        api_input = self.build_payload(prompt, self.params)

        # Create the JSON payload by merging additional_input and api_input
        json = {**self.additional_input, **api_input}

        # Build headers based on auth_config
        logger.debug(f"Building headers")
        headers = self._build_headers()

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.endpoint,
                    json=json,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.timeout),
                ) as response:
                    # Debug logging: Log response metadata
                    response_size = response.headers.get(
                        'content-length', 'unknown')
                    logger.debug(f"API Response - Status: {response.status}")
                    logger.debug(
                        f"API Response - Content-Length: {response_size}")

                    response_json = await response.json()

                    api_text_output = self.extract_output(
                        response_json, self.params
                    )

                    if not isinstance(api_text_output, str):
                        raise ValueError(
                            f"API output must be a string, got {type(api_text_output)}: {api_text_output}"
                        )

                    return ConnectorResponse(
                        response=api_text_output, context=[]
                    )
        except Exception as e:
            logger.info(f"Error in API call: {e}")
            raise e
