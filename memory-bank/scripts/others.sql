select
  *
from
  products
  join tenants on tenants.id = products.tenant_id
  join endpoints on endpoints.product_id = products.id
where
  tenants.name = 'GT-AIG-David-test-2';

select
  *
from
  endpoints
order by
  created_at desc;

select
  *
from
  products
  join endpoints on endpoints.product_id = products.id
order by
  endpoints.created_at desc;

-- Update tenant name
UPDATE tenants
SET
  name = 'CAAS/EDMS'
WHERE
  name = 'CAAS AIGuardian';

-- Update product name
UPDATE products
SET
  name = 'Product test by David 2'
WHERE
  id = 'c6fb5b6d-f391-4396-a24e-2bfd878167f4';


-- DELETE endpoint
DELETE FROM endpoints
WHERE
  id = '00000000-0000-0000-0000-000000000000';